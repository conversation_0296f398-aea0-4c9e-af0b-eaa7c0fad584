services:
  nextjs:
    container_name: next-app
    build:
      context: .
      dockerfile: Dockerfile

    # Set environment variables directly in the compose file
    #    environment:
    #      ENV_VARIABLE: ${ENV_VARIABLE}
    #      NEXT_PUBLIC_ENV_VARIABLE: ${NEXT_PUBLIC_ENV_VARIABLE}

    # Set environment variables based on the .env file
    env_file:
      - .env
    volumes:
      - ./next-app/src:/app/src
      - ./next-app/public:/app/public
    restart: always
    ports:
      - "3000:3000"
    networks:
      - app-network

  # Add more containers below (nginx, postgres, etc.)

# Define a network, which allows containers to communicate
# with each other, by using their container name as a hostname
networks:
  app-network:
    driver: bridge

