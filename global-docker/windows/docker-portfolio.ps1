# PowerShell script for managing Docker portfolio containers on Windows
# Usage: .\docker-portfolio.ps1 [command]

param(
    [Parameter(Position=0)]
    [string]$Command = "help"
)

# Color functions for better output
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Success { Write-ColorOutput Green $args }
function Write-Info { Write-ColorOutput Cyan $args }
function Write-Warning { Write-ColorOutput Yellow $args }
function Write-Error { Write-ColorOutput Red $args }

# Function to check if Docker is running
function Test-DockerRunning {
    try {
        docker info | Out-Null
        return $true
    } catch {
        return $false
    }
}

# Function to start containers
function Start-Containers {
    Write-Info "Starting Docker containers..."
    
    if (-not (Test-DockerRunning)) {
        Write-Error "Docker is not running. Please start Docker Desktop first."
        return
    }
    
    try {
        docker compose up --build -d
        Write-Success "Services are now running at:"
        Write-Success "- Admin Backend: http://localhost:8080"
        Write-Success "- Admin Frontend: http://localhost:3000"
        Write-Success "- Portfolio: http://localhost:5000"
        Write-Info ""
        Write-Info "To view logs, run: .\docker-portfolio.ps1 logs"
        Write-Info "To stop containers, run: .\docker-portfolio.ps1 stop"
    } catch {
        Write-Error "Failed to start containers: $_"
    }
}

# Function to stop containers
function Stop-Containers {
    Write-Info "Stopping Docker containers..."
    try {
        docker compose down
        Write-Success "All containers stopped successfully."
    } catch {
        Write-Error "Failed to stop containers: $_"
    }
}

# Function to restart containers
function Restart-Containers {
    Write-Info "Restarting Docker containers..."
    Stop-Containers
    Start-Sleep -Seconds 2
    Start-Containers
}

# Function to show logs
function Show-Logs {
    param([string]$Service = "")
    
    if ($Service) {
        Write-Info "Showing logs for $Service..."
        docker compose logs -f $Service
    } else {
        Write-Info "Showing logs from all containers..."
        docker compose logs -f
    }
}

# Function to show container status
function Show-Status {
    Write-Info "Container status:"
    docker compose ps
}

# Function to rebuild containers
function Rebuild-Containers {
    Write-Info "Rebuilding containers..."
    try {
        docker compose down
        docker compose build --no-cache
        docker compose up -d
        Write-Success "Containers rebuilt successfully."
    } catch {
        Write-Error "Failed to rebuild containers: $_"
    }
}

# Function to clean up Docker resources
function Clean-Docker {
    Write-Warning "This will remove all stopped containers, unused networks, and dangling images."
    $confirmation = Read-Host "Are you sure? (y/N)"
    
    if ($confirmation -eq 'y' -or $confirmation -eq 'Y') {
        Write-Info "Cleaning up Docker resources..."
        docker system prune -f
        Write-Success "Docker cleanup completed."
    } else {
        Write-Info "Cleanup cancelled."
    }
}

# Function to show help
function Show-Help {
    Write-Info "Docker Portfolio Management Script for Windows"
    Write-Info "Usage: .\docker-portfolio.ps1 [command]"
    Write-Info ""
    Write-Info "Available commands:"
    Write-Info "  start              Start all containers"
    Write-Info "  stop               Stop all containers"
    Write-Info "  restart            Restart all containers"
    Write-Info "  logs               Show logs from all containers"
    Write-Info "  logs:admin-backend Show logs from admin-backend only"
    Write-Info "  logs:admin-frontend Show logs from admin-frontend only"
    Write-Info "  logs:portfolio     Show logs from portfolio only"
    Write-Info "  status             Show container status"
    Write-Info "  rebuild            Rebuild all containers"
    Write-Info "  clean              Clean up Docker resources"
    Write-Info "  help               Show this help message"
    Write-Info ""
    Write-Info "Examples:"
    Write-Info "  .\docker-portfolio.ps1 start"
    Write-Info "  .\docker-portfolio.ps1 logs:admin-backend"
    Write-Info "  .\docker-portfolio.ps1 rebuild"
}

# Main script logic
switch ($Command.ToLower()) {
    "start" { Start-Containers }
    "stop" { Stop-Containers }
    "restart" { Restart-Containers }
    "logs" { Show-Logs }
    "logs:admin-backend" { Show-Logs -Service "admin-backend" }
    "logs:admin-frontend" { Show-Logs -Service "admin-frontend" }
    "logs:portfolio" { Show-Logs -Service "portfolio" }
    "status" { Show-Status }
    "rebuild" { Rebuild-Containers }
    "clean" { Clean-Docker }
    "help" { Show-Help }
    default { 
        Write-Error "Unknown command: $Command"
        Write-Info ""
        Show-Help
    }
}
