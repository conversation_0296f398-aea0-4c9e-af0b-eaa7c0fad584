#!/bin/bash

# Function to display help message
show_help() {
  echo "Docker Development Helper Script"
  echo "--------------------------------"
  echo "Usage: ./docker-dev.sh [command]"
  echo ""
  echo "Commands:"
  echo "  start       - Start Docker containers in detached mode"
  echo "  stop        - Stop Docker containers"
  echo "  restart     - Restart Docker containers"
  echo "  logs        - Show logs from Docker containers"
  echo "  refresh     - Restart only the Next.js development server without restarting Docker"
  echo "  help        - Show this help message"
  echo ""
}

# Start Docker containers
start_containers() {
  echo "Starting Docker containers..."
  docker-compose up --build -d
  echo "Next.js application is now running at http://localhost:3000"
}

# Stop Docker containers
stop_containers() {
  echo "Stopping Docker containers..."
  docker-compose down
}

# Restart Docker containers
restart_containers() {
  stop_containers
  start_containers
}

# Show logs
show_logs() {
  echo "Showing logs from Docker containers..."
  docker-compose logs -f
}

# Refresh Next.js server without restarting Docker
refresh_nextjs() {
  echo "Refreshing Next.js development server..."
  # Find the container ID
  CONTAINER_ID=$(docker-compose ps -q nextjs-app)
  
  if [ -z "$CONTAINER_ID" ]; then
    echo "Error: Next.js container not found. Is it running?"
    exit 1
  fi
  
  # Send SIGTERM to the Node.js process (Next.js dev server)
  # This will cause Next.js to restart without restarting the container
  docker exec $CONTAINER_ID sh -c "pkill -f 'node.*dev'"
  
  echo "Next.js development server is restarting..."
  echo "You can check the logs with: ./docker-dev.sh logs"
}

# Main script logic
case "$1" in
  start)
    start_containers
    ;;
  stop)
    stop_containers
    ;;
  restart)
    restart_containers
    ;;
  logs)
    show_logs
    ;;
  refresh)
    refresh_nextjs
    ;;
  help|*)
    show_help
    ;;
esac
