# Docker Compose configuration for all portfolio projects
services:
  # Admin Backend (NestJS)
  admin-backend:
    build:
      context: ./admin-backend
      dockerfile: Dockerfile
    container_name: portfolio-admin-backend
    ports:
      - "8080:8080"
    env_file:
      - ./admin-backend/.env
    environment:
      - NODE_ENV=development
      - CORS_ORIGIN=http://localhost:3000
    volumes:
      - ./admin-backend:/app
      - /app/node_modules
    command: npm run start:dev
    restart: unless-stopped
    networks:
      - portfolio-network

  # Admin Frontend (Next.js)
  admin-frontend:
    build:
      context: ./admin-frontend
      dockerfile: Dockerfile
    container_name: portfolio-admin-frontend
    ports:
      - "3000:3000"
    env_file:
      - ./admin-frontend/.env
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_APP_URL=http://localhost:3000
      - NEXT_PUBLIC_SERVER_URL=http://localhost:8080/api
      - API_URL=http://localhost:8080/api/
      - API_VERSION=v1/
    volumes:
      - ./admin-frontend:/app
      - /app/node_modules
      - /app/.next
    command: pnpm dev
    restart: unless-stopped
    depends_on:
      - admin-backend
    networks:
      - portfolio-network

  # Portfolio Frontend (Next.js)
  portfolio:
    build:
      context: ./portfolio
      dockerfile: Dockerfile
    container_name: portfolio-frontend
    ports:
      - "5000:5000"
    env_file:
      - ./portfolio/.env.local
    environment:
      - NODE_ENV=development
    volumes:
      - ./portfolio:/app
      - /app/node_modules
      - /app/.next
    command: pnpm dev
    restart: unless-stopped
    networks:
      - portfolio-network

# Define a network for all services to communicate
networks:
  portfolio-network:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: **********/16
