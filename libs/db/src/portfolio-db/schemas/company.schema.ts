import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document } from 'mongoose';
import { User } from '@app/db/portfolio-db/schemas/user.schema';

export type CompanyDocument = Company & Document;

export class CompanySocialMediaEntity {
  title: string;
  logo: string;
  href: string;
}

@Schema({
  _id: false,
  timestamps: false,
  versionKey: false,
})
class SocialMedia {
  @Prop({ type: String, trim: true, default: null, nullable: true })
  title: string;

  @Prop({ type: String, trim: true, default: null, nullable: true })
  logo: string;

  @Prop({ type: String, trim: true, default: null, nullable: true })
  href: string;
}
const socialMediaSchema = SchemaFactory.createForClass(SocialMedia);

@Schema({
  timestamps: true,
  versionKey: false,
})
export class Company extends Document {
  @Prop({ type: String, trim: true })
  name: string;

  @Prop({ type: String, trim: true })
  designation: string;

  @Prop({ type: Date, trim: true, required: true })
  startedAt: Date;

  @Prop({ type: Date, trim: true })
  endedAt: Date;

  @Prop({ type: String, trim: true })
  workArea: string;

  @Prop({ type: String, trim: true })
  workDescription: string;

  @Prop({ type: String, trim: true })
  companyDescription: string;

  @Prop({ type: String, trim: true })
  phone: string;

  @Prop({ type: String, trim: true })
  companyWebsite: string;

  @Prop({ type: String, trim: true })
  logo: string;

  @Prop({ type: Number, trim: true })
  salary: number;

  @Prop({ type: String, trim: true })
  currency: string;

  @Prop({ type: Boolean, default: true })
  active: boolean;

  @Prop({ type: Boolean, trim: true, default: false })
  isCurrent: boolean;

  @Prop({ type: Boolean, trim: true, default: true })
  isActive: boolean;

  @Prop({ type: Boolean, trim: true, default: false })
  isPromoted: boolean;

  @Prop({ type: socialMediaSchema, default: null })
  socialMedia: CompanySocialMediaEntity;

  @Prop({ type: String, trim: true })
  country: string;

  @Prop({ type: String, trim: true })
  address: string;

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true,
  })
  user: User;

  @Prop({ type: Date, default: Date.now })
  createdAt: Date;

  @Prop({ type: Date, default: Date.now })
  updatedAt: Date;
}

const schema = SchemaFactory.createForClass(Company);
export const CompanySchema = schema;
