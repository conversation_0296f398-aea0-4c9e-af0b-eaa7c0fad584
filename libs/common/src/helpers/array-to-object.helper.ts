/**
 * Converts an array into an object with keys derived from each item.
 *
 * @param arr - The array to convert.
 * @param keySelector - A property name (key of T) or a function that returns a key for an item.
 * @returns An object with keys generated from each item in the array.
 */
export function arrayToObject<T, K extends keyof any>(
  arr: T[],
  keySelector: ((item: T) => K) | keyof T,
): Record<K, T> {
  return arr.reduce<Record<K, T>>(
    (obj, item) => {
      const key =
        typeof keySelector === 'function'
          ? keySelector(item)
          : (item[keySelector as keyof T] as unknown as K);
      obj[key] = item;
      return obj;
    },
    {} as Record<K, T>,
  );
}
