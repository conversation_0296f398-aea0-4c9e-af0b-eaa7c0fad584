
/* -------------Progress Bar  ------------------- */

.skill-title{
  font-size: 20px;
  font-weight: 650;
  margin-bottom: 10px;
  text-decoration: underline;
  
}

.wrapper {
  border: 1px solid rgb(68, 68, 73);
  border-radius: 8px 0px 8px 0px;
}

.container {
  background-color: rgb(29, 54, 70);
}


.label {
  display: flex;
  justify-content: center;
  font-size: 12px;
  color: rgb(126, 213, 126);
}


.progress-bar {
  width: 50%;
  height: 20px;
  background-color: #607B96;
  border-radius: 10px;
  position: relative;
}

.skillDiv{
  width: 70%;
}

.progress {
  height: 20px !important;
  background-color: #2f52bb;  
  border-radius: 10px 0px 0px 10px;
  position: absolute;
  
}
.progress-label{
  font-size: 12px;
  font-weight: 600;
  color: rgba(11, 32, 113, 0.922);
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 10%;
  left: 50%;
}
