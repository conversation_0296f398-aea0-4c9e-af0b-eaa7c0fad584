#skill {
  @apply font-fira_regular
}

.left-skills-div{
  width: 50%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 1% 2%; 
}

.right-skills-div{
  width: 50%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 1% 2%; 
}


/* -------------Progress Bar  ------------------- */

.skill-title{
  font-size: 20px;
  font-weight: 650;
  margin-bottom: 10px;
  text-decoration: underline;
  
}

.skillDiv{
  width: 70%;
}


@media (max-width: 1020px){
   .left-skills-div{
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 2% 3%; 
}


.right-skills-div{
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 2% 3%; 
} 



.label {
  display: flex;
  justify-content: center;
  font-size: 12px;
  color: rgb(126, 213, 126);
}


.progress-bar {
  width: 50%;
  height: 20px;
  background-color: #607B96;
  border-radius: 10px;
  position: relative;
}

.skillDiv{
  width: 90%;
}

.progress {
  height: 100%;
  background-color: #2f52bb;
  border-radius: 10px 0px 0px 10px;
  position: absolute;
}
.progress-label{
  font-size: 12px;
  font-weight: 600;
  color: rgba(11, 32, 113, 0.922);
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 10%;
  left: 50%;

}
}