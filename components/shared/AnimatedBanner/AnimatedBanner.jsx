/*
 * Filename: /home/<USER>/WorkStation/myportfolio/components/shared/AnimatedBanner/AnimatedBanner.jsx
 * Path: /home/<USER>/WorkStation/myportfolio
 * Created Date: Wednesday, July 12th 2023, 8:17:03 pm
 * Author: <PERSON><PERSON><PERSON>
 *
 * Copyright (c) 2023 Tanzi<PERSON> Ahmed
 */
"use client";
// import { CardBody, CardContainer, CardItem } from "";
import {CardBody, CardContainer, CardItem} from '@/components/aceternity/3dCard';
import Image from 'next/image';
import ProPic from '/Assets/images/personal/pro-pic.png';
import {useCommonData} from '@/hooks/useCommonData';
import './index.css'

const AnimatedBanner = () => {
  const {
    loading,
    error,
    data,
    getUserName,
    getCurrentCompany,
    getCurrentDesignation,
    getUserProfileImage
  } = useCommonData();
  // Loading state for AnimatedBanner
  if (loading) {
    return (
      <>
        <CardContainer className="inter-var ">
          <CardBody className="  bg-gray-800
           relative group/card  dark:hover:shadow-2xl dark:hover:shadow-emerald-500/[0.1]  dark:border-white/[0.2] border-black/[0.1] w-auto sm:w-[30rem] h-auto rounded-xl p-6 border  ">

            <CardItem translateZ="50" className="text-xl font-bold text-zinc-300  ">
              <div className="animated-banner-skeleton">
                <div className="skeleton-text" style={{width: '280px', height: '24px'}}></div>
              </div>
            </CardItem>

            <CardItem translateZ="100" className="w-full mt-4">
              <div className={ "flex justify-between items-center"}>
                <div className="skeleton-image" style={{width: '140px', height: '160px', backgroundColor: '#374151', borderRadius: '12px'}}></div>

                <div className="text-gray-300 desc">
                  <div className="animated-banner-skeleton">
                    <div className="skeleton-text" style={{width: '100px', height: '12px', marginBottom: '8px'}}></div>
                    <div className="skeleton-text" style={{width: '120px', height: '12px', marginBottom: '8px'}}></div>
                    <div className="skeleton-text" style={{width: '140px', height: '12px', marginBottom: '8px'}}></div>
                    <div className="skeleton-text" style={{width: '110px', height: '12px', marginBottom: '16px'}}></div>
                    <div className="skeleton-text" style={{width: '90px', height: '12px', marginBottom: '8px'}}></div>
                    <div className="skeleton-text" style={{width: '130px', height: '12px', marginBottom: '8px'}}></div>
                    <div className="skeleton-text" style={{width: '100px', height: '12px'}}></div>
                  </div>
                </div>
              </div>
            </CardItem>

          </CardBody>
        </CardContainer>
      </>
    );
  }

  // Get dynamic data with fallbacks
  const userName = getUserName();
  const companyName = getCurrentCompany();
  const designation = getCurrentDesignation();
  const userEmail = data?.user?.email || '<EMAIL>';
  const userPhone = data?.user?.phone || '+880 ************';
  const userImage = data?.config?.image || '';

  // Get user profile image with fallback
  const profileImage = getUserProfileImage() || ProPic;

  // Get company details
  const companyAddress = data?.company?.address || 'Road no #24, Gulshan 2,';
  const companyCountry = data?.company?.country || 'Dhaka, Bangladesh';

  return (
    <>
      <CardContainer className="inter-var ">
        <CardBody className="  bg-gray-800
         relative group/card  dark:hover:shadow-2xl dark:hover:shadow-emerald-500/[0.1]  dark:border-white/[0.2] border-black/[0.1] w-auto sm:w-[30rem] h-auto rounded-xl p-6 border  ">

          <CardItem translateZ="50" className="text-xl font-bold text-zinc-300  ">
            <span className="text-cyan-500">{designation}</span>  at @<span className="text-orange-500"> {companyName}</span>
          </CardItem>

          <CardItem translateZ="100" className="w-full mt-4">
            <div className={ "flex justify-between items-center"}>
              <Image
                src={userImage}
                height="120"
                width="140"
                className=" h-40 object-cover  group-hover/card:shadow-xl rounded-tl-3xl"
                alt="Profile picture"
                onError={(e) => {
                  e.target.src = ProPic; // Fallback to default image on error
                }}
              />

              <div className="text-gray-300 desc">
                <p>{userName}</p>
                <p>{designation}</p>
                <p>{userEmail}</p>
                <p>{userPhone}</p>
                <br/>
                <a className='text-orange-500' href="#" target="_blank" rel="noopener noreferrer">{companyName}</a>
                <p>{companyAddress}</p>
                <p>{companyCountry}</p>
              </div>
            </div>
          </CardItem>

        </CardBody>
      </CardContainer>
    </>

  );
};

export default AnimatedBanner;
