'use client'

import React, { ReactNode } from 'react'

// MUI Imports
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import Divider from '@mui/material/Divider'
import Button from '@mui/material/Button'
import Typography from '@mui/material/Typography'
import Snackbar from '@mui/material/Snackbar'
import Alert from '@mui/material/Alert'

type EntityFormProps = {
  title: string
  subheader?: string
  isEditMode: boolean
  isViewMode: boolean
  loading: boolean
  fetchLoading: boolean
  notification: {
    show: boolean
    message: string
    type: 'success' | 'error' | 'info' | 'warning'
  }
  setNotification: React.Dispatch<
    React.SetStateAction<{
      show: boolean
      message: string
      type: 'success' | 'error' | 'info' | 'warning'
    }>
  >
  handleSubmit: () => void
  onSubmit: (data: any) => Promise<void>
  reset: () => void
  router: any
  cancelPath: string
  children: ReactNode
}

const EntityForm: React.FC<EntityFormProps> = ({
  title,
  subheader,
  isEditMode,
  isViewMode,
  loading,
  fetchLoading,
  notification,
  setNotification,
  handleSubmit,
  onSubmit,
  reset,
  router,
  cancelPath,
  children
}) => {
  if (fetchLoading) {
    return (
      <Card>
        <CardHeader title={title} />
        <CardContent>
          <Typography>Loading data...</Typography>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader title={title} subheader={subheader} />
      <Divider />
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)}>
          {children}

          {!isViewMode && (
            <div className='flex gap-4 flex-wrap mt-6'>
              <Button variant='contained' type='submit' disabled={loading}>
                {loading ? 'Saving...' : isEditMode ? 'Update' : 'Save'}
              </Button>
              <Button variant='outlined' type='reset' color='secondary' onClick={() => reset()} disabled={loading}>
                Reset
              </Button>
              <Button variant='outlined' color='primary' onClick={() => router.push(cancelPath)} disabled={loading}>
                Cancel
              </Button>
            </div>
          )}

          {isViewMode && (
            <div className='flex gap-4 flex-wrap mt-6'>
              <Button
                variant='contained'
                color='primary'
                onClick={() => {
                  // Get the current URL search params
                  const searchParams = new URLSearchParams(window.location.search)
                  const id = searchParams.get('id')
                  router.push(`${cancelPath}?id=${id}&mode=edit`)
                }}
              >
                Edit
              </Button>
              <Button variant='outlined' color='primary' onClick={() => router.push(cancelPath)}>
                Back to List
              </Button>
            </div>
          )}
        </form>
      </CardContent>

      {/* Notification */}
      <Snackbar
        open={notification.show}
        autoHideDuration={6000}
        onClose={() => setNotification({ ...notification, show: false })}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setNotification({ ...notification, show: false })}
          severity={notification.type}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Card>
  )
}

export default EntityForm
