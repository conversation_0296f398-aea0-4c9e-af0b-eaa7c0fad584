'use client'

import { useEffect, useState } from 'react'

export interface SocialMediaItem {
  id?: string
  name: string
  link: string
  logo?: string
  description: string
  active: boolean
  user?: string // User ID
  createdAt?: Date
  updatedAt?: Date
}

export interface SocialMediaItemWithUser extends SocialMediaItem {
  userDetails?: {
    id: string
    name: string
    email: string
    profileImage?: string
  }
}

export const useAdminSocialMedia = () => {
  const [socialMediaItems, setSocialMediaItems] = useState<SocialMediaItemWithUser[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Fetch all social media items for admin (uses original endpoint)
  const fetchAllSocialMedia = async () => {
    try {
      setLoading(true)
      setError(null)

      // Use the original social-media endpoint which shows all users' data for admin
      const response = await fetch('/api/social-media')
      if (!response.ok) {
        throw new Error('Failed to fetch social media data')
      }

      const data = await response.json()
      const socialMediaItems = Array.isArray(data) ? data : data.data || []

      // Map social media items and use the user data that's already populated
      const socialMediaWithUsers = socialMediaItems.map((item: any) => {
        return {
          ...item,
          userDetails: item.user // User data is already populated by the backend
        }
      })

      setSocialMediaItems(socialMediaWithUsers)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch social media data'
      console.error('Error fetching social media:', err)
      setError(errorMessage)
      setSocialMediaItems([])
    } finally {
      setLoading(false)
    }
  }

  // Delete social media item
  const deleteSocialMedia = async (id: string): Promise<boolean> => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/social-media/${id}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        throw new Error('Failed to delete social media')
      }

      // Update local state
      setSocialMediaItems(prev => prev.filter(item => item.id !== id))
      return true
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete social media'
      setError(errorMessage)
      return false
    } finally {
      setLoading(false)
    }
  }

  // Toggle active status
  const toggleSocialMediaActive = async (id: string, active: boolean): Promise<boolean> => {
    try {
      setLoading(true)
      setError(null)

      const formData = new FormData()
      formData.append('active', active.toString())

      const response = await fetch(`/api/social-media/${id}`, {
        method: 'PATCH',
        body: formData
      })

      if (!response.ok) {
        throw new Error('Failed to update social media status')
      }

      const updatedItem = await response.json()

      // Update local state
      setSocialMediaItems(prev => prev.map(item => (item.id === id ? { ...item, active: updatedItem.active } : item)))
      return true
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update social media status'
      setError(errorMessage)
      return false
    } finally {
      setLoading(false)
    }
  }

  // Auto-fetch on mount
  useEffect(() => {
    fetchAllSocialMedia()
  }, [])

  return {
    socialMediaItems,
    loading,
    error,
    fetchAllSocialMedia,
    deleteSocialMedia,
    toggleSocialMediaActive
  }
}
