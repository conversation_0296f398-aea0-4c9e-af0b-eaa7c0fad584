import axios from 'axios'
import { getAuthHeader } from '@/utils/auth'
import { createErrorResponse } from '@/utils/errorHandling'

const baseUrl = `${process.env.NEXT_PUBLIC_SERVER_URL}/config`

export async function GET(request: Request, { params }: { params: { id: string } }) {
  const { id } = params

  try {
    const authHeader = await getAuthHeader()

    const res = await axios.get(`${baseUrl}/${id}`, {
      headers: {
        ...authHeader
      }
    })

    const data = res.data

    return new Response(JSON.stringify(data), {
      status: res.status,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  } catch (error) {
    return createErrorResponse(error, 'Failed to fetch configuration')
  }
}

export async function PATCH(request: Request, { params }: { params: { id: string } }) {
  const { id } = params

  try {
    const authHeader = await getAuthHeader()

    // Handle FormData for file uploads
    const formData = await request.formData()

    const res = await axios.patch(`${baseUrl}/${id}`, formData, {
      headers: {
        ...authHeader,
        'Content-Type': 'multipart/form-data'
      }
    })

    const data = res.data

    return new Response(JSON.stringify(data), {
      status: res.status,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  } catch (error) {
    return createErrorResponse(error, 'Failed to update configuration')
  }
}

export async function DELETE(request: Request, { params }: { params: { id: string } }) {
  const { id } = params

  try {
    const authHeader = await getAuthHeader()

    const res = await axios.delete(`${baseUrl}/${id}`, {
      headers: {
        ...authHeader
      }
    })

    const data = res.data

    return new Response(JSON.stringify(data), {
      status: res.status,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  } catch (error) {
    return createErrorResponse(error, 'Failed to delete configuration')
  }
}
