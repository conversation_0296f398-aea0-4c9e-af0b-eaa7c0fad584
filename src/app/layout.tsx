// Next Imports
import { Inter } from 'next/font/google'

// Third-party Imports
import 'react-perfect-scrollbar/dist/css/styles.css'

// Type Imports
import type { ChildrenType } from '@core/types'

// Style Imports
import '@/app/globals.css'

// Generated Icon CSS Imports
import '@assets/iconify-icons/generated-icons.css'

const inter = Inter({ subsets: ['latin'], weight: ['300', '400', '500', '600', '700', '800', '900'] })

export const metadata = {
  title: "Tanzim's Portfolio Panel",
  description:
    "Tanzim's Portfolio Panel is a web application that showcases my projects, skills, and experiences. It serves as a digital portfolio to demonstrate my work and expertise in various fields."
}

const RootLayout = ({ children }: ChildrenType) => {

  const direction = 'ltr'

  return (
    <html id='__next' dir={direction}>
      <body className={`flex is-full min-bs-full flex-auto flex-col ${inter.className}`}>{children}</body>
    </html>
  )
}

export default RootLayout
