import React, { type ReactElement } from 'react'

import dynamic from 'next/dynamic'

import Education from '@views/education'

const EducationTab = dynamic(() => import('@views/education/education'))
const AllTab = dynamic(() => import('@views/education/all'))

const tabContentList = (): { [key: string]: ReactElement } => ({
  all: <AllTab />,
  education: <EducationTab />
})

const Page = () => {
  return <Education tabContentList={tabContentList()} />
}

export default Page
