'use client'

// React Imports
import { type ReactElement, type SyntheticEvent, useEffect, useState } from 'react'

// Next Imports
import { useRouter, useSearchParams } from 'next/navigation'

// MUI Imports
import Grid from '@mui/material/Grid'
import Tab from '@mui/material/Tab'
import TabContext from '@mui/lab/TabContext'
import TabList from '@mui/lab/TabList'
import TabPanel from '@mui/lab/TabPanel'

const Roles = ({ tabContentList }: { tabContentList: { [key: string]: ReactElement } }) => {
  // Hooks
  const searchParams = useSearchParams()
  const router = useRouter()
  const tabParam = searchParams.get('tab')

  // States
  const [activeTab, setActiveTab] = useState(tabParam || 'all')

  // Update URL when tab changes
  const handleChange = (event: SyntheticEvent, value: string) => {
    setActiveTab(value)
    router.push(`/roles?tab=${value}`)
  }

  // Update active tab if URL parameter changes
  useEffect(() => {
    if (tabParam && tabContentList[tabParam]) {
      setActiveTab(tabParam)
    }
  }, [tabParam, tabContentList])

  return (
    <TabContext value={activeTab}>
      <Grid container spacing={6}>
        <Grid item xs={12}>
          <TabList onChange={handleChange} variant='scrollable'>
            <Tab label='All Roles' icon={<i className='ri-list-check' />} iconPosition='start' value='all' />
            <Tab label='Add Role' icon={<i className='ri-shield-user-line' />} iconPosition='start' value='role' />
          </TabList>
        </Grid>
        <Grid item xs={12}>
          <TabPanel value={activeTab} className='p-0'>
            {tabContentList[activeTab]}
          </TabPanel>
        </Grid>
      </Grid>
    </TabContext>
  )
}

export default Roles
