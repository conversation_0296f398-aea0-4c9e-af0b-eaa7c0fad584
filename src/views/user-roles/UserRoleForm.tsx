'use client'

// React Imports
import { useEffect, useState } from 'react'

// Next Imports
import { useRouter, useSearchParams } from 'next/navigation'

// MUI Imports
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import Grid from '@mui/material/Grid'
import TextField from '@mui/material/TextField'
import FormControl from '@mui/material/FormControl'
import InputLabel from '@mui/material/InputLabel'
import Select from '@mui/material/Select'
import MenuItem from '@mui/material/MenuItem'
import FormControlLabel from '@mui/material/FormControlLabel'
import Switch from '@mui/material/Switch'
import Button from '@mui/material/Button'
import Typography from '@mui/material/Typography'
import Box from '@mui/material/Box'
import FormGroup from '@mui/material/FormGroup'
import Checkbox from '@mui/material/Checkbox'
import Snackbar from '@mui/material/Snackbar'
import Alert from '@mui/material/Alert'
import CircularProgress from '@mui/material/CircularProgress'

// Type Imports
import type { CreateUserRoleDto, UpdateUserRoleDto, UserRoleEntity } from '@/types/user-role'
import type { RoleEntity } from '@/types/role'
import { PERMISSIONS } from '@/types/role'

interface User {
  id: string
  name: string
  email: string
}

const UserRoleForm = () => {
  // Hooks
  const searchParams = useSearchParams()
  const router = useRouter()

  // Get URL params
  const userRoleId = searchParams.get('id')
  const mode = searchParams.get('mode') // 'view', 'edit', or null (create)

  // Determine form mode
  const isEditMode = mode === 'edit' && userRoleId
  const isViewMode = mode === 'view' && userRoleId
  const isCreateMode = !mode && !userRoleId

  // States
  const [formData, setFormData] = useState<UserRoleEntity>({
    user: '',
    role: '',
    additionalPermissions: [],
    deniedPermissions: [],
    isActive: true,
    isDefault: false,
    notes: ''
  })

  const [users, setUsers] = useState<User[]>([])
  const [roles, setRoles] = useState<RoleEntity[]>([])
  const [selectedRole, setSelectedRole] = useState<RoleEntity | null>(null)
  const [loading, setLoading] = useState(false)
  const [fetchLoading, setFetchLoading] = useState(false)
  const [notification, setNotification] = useState<{
    show: boolean
    message: string
    type: 'success' | 'error' | 'warning' | 'info'
  }>({
    show: false,
    message: '',
    type: 'success'
  })

  // Fetch initial data
  useEffect(() => {
    const loadData = async () => {
      setFetchLoading(true)
      try {
        await Promise.all([fetchUsers(), fetchRoles()])
        if (userRoleId && (isEditMode || isViewMode)) {
          await fetchUserRole()
        }
      } finally {
        setFetchLoading(false)
      }
    }
    loadData()
  }, [userRoleId, isEditMode, isViewMode])

  // Update selected role when roles or formData changes
  useEffect(() => {
    if (formData.role && roles.length > 0) {
      const role = roles.find(r => r.id === formData.role)
      if (role) {
        setSelectedRole(role)
      }
    }
  }, [formData.role, roles])

  // Fetch users
  const fetchUsers = async () => {
    try {
      const response = await fetch('/api/user')
      if (response.ok) {
        const data = await response.json()
        setUsers(data.data || data)
      }
    } catch (error) {
      console.error('Error fetching users:', error)
    }
  }

  // Fetch roles
  const fetchRoles = async () => {
    try {
      const response = await fetch('/api/roles')
      if (response.ok) {
        const data = await response.json()
        setRoles(data.data || data)
      }
    } catch (error) {
      console.error('Error fetching roles:', error)
    }
  }

  // Fetch user role data for edit/view mode
  const fetchUserRole = async () => {
    try {
      const response = await fetch(`/api/user-roles/${userRoleId}`)

      if (!response.ok) {
        const errorText = await response.text()
        console.error('Failed to fetch user role:', errorText)
        throw new Error('Failed to fetch user role')
      }

      const data = await response.json()
      const userRoleData = data.data || data

      // Ensure permissions arrays are properly initialized
      const processedData = {
        ...userRoleData,
        additionalPermissions: userRoleData.additionalPermissions || [],
        deniedPermissions: userRoleData.deniedPermissions || []
      }
      setFormData(processedData)

      // The selected role will be set by the useEffect that watches formData.role and roles
    } catch (error) {
      console.error('Error fetching user role:', error)
      setNotification({
        show: true,
        message: 'Failed to fetch user role data',
        type: 'error'
      })
    }
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (isViewMode) return

    try {
      setLoading(true)

      const url = isEditMode ? `/api/user-roles/${userRoleId}` : '/api/user-roles'
      const method = isEditMode ? 'PATCH' : 'POST'

      const payload: CreateUserRoleDto | UpdateUserRoleDto = isEditMode
        ? {
            // UpdateUserRoleDto - include roleId to allow role changes
            roleId: formData.role,
            additionalPermissions: formData.additionalPermissions,
            deniedPermissions: formData.deniedPermissions,
            isActive: formData.isActive,
            isDefault: formData.isDefault,
            expiresAt: formData.expiresAt,
            notes: formData.notes
          }
        : {
            // CreateUserRoleDto - includes userId/roleId for new assignments
            userId: formData.user,
            roleId: formData.role,
            additionalPermissions: formData.additionalPermissions,
            deniedPermissions: formData.deniedPermissions,
            isActive: formData.isActive,
            isDefault: formData.isDefault,
            expiresAt: formData.expiresAt,
            notes: formData.notes
          }

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.error('Failed to update user role:', errorText)
        throw new Error(`Failed to ${isEditMode ? 'update' : 'assign'} user role: ${errorText}`)
      }

      setNotification({
        show: true,
        message: `User role ${isEditMode ? 'updated' : 'assigned'} successfully`,
        type: 'success'
      })

      // Redirect to all user roles after successful creation/update
      setTimeout(() => {
        router.push('/user-roles?tab=all')
      }, 1500)
    } catch (error) {
      console.error('Error submitting form:', error)
      setNotification({
        show: true,
        message: `Failed to ${isEditMode ? 'update' : 'assign'} user role`,
        type: 'error'
      })
    } finally {
      setLoading(false)
    }
  }

  // Handle input changes
  const handleInputChange = (field: keyof UserRoleEntity, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  // Handle role selection
  const handleRoleChange = (roleId: string) => {
    const role = roles.find(r => r.id === roleId)
    setSelectedRole(role || null)
    handleInputChange('role', roleId)
  }

  // Handle permission toggle
  const handlePermissionToggle = (permission: string, type: 'additional' | 'denied') => {
    if (isViewMode) return

    // Don't allow toggling role permissions in additional permissions
    if (type === 'additional' && selectedRole?.permissions.includes(permission)) {
      return
    }

    // Don't allow toggling non-role permissions in denied permissions
    if (type === 'denied' && !selectedRole?.permissions.includes(permission)) {
      return
    }

    const field = type === 'additional' ? 'additionalPermissions' : 'deniedPermissions'
    const currentPermissions = formData[field]
    const newPermissions = currentPermissions.includes(permission)
      ? currentPermissions.filter(p => p !== permission)
      : [...currentPermissions, permission]

    setFormData(prev => ({
      ...prev,
      [field]: newPermissions
    }))
  }

  if (fetchLoading) {
    return (
      <Card>
        <CardContent className='flex justify-center items-center py-8'>
          <CircularProgress />
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader
        title={isEditMode ? 'Edit User Role' : isViewMode ? 'View User Role' : 'Assign Role to User'}
        subheader={
          isEditMode ? 'Update user role assignment' : isViewMode ? 'View user role details' : 'Assign a role to a user'
        }
      />
      <CardContent>
        <form onSubmit={handleSubmit}>
          <Grid container spacing={6}>
            {/* User and Role Selection */}
            {(isCreateMode || isEditMode || isViewMode) && (
              <>
                <Grid item xs={12} md={6}>
                  {isViewMode ? (
                    <TextField
                      fullWidth
                      label='User'
                      value={users.find(u => u.id === formData.user)?.name || 'Loading...'}
                      disabled
                      helperText={users.find(u => u.id === formData.user)?.email || ''}
                    />
                  ) : (
                    <FormControl fullWidth required>
                      <InputLabel>User</InputLabel>
                      <Select
                        value={formData.user}
                        label='User'
                        onChange={e => handleInputChange('user', e.target.value)}
                        disabled={isViewMode || isEditMode} // Disable in view mode and edit mode (can't change user)
                      >
                        {users.map(user => (
                          <MenuItem key={user.id} value={user.id}>
                            {user.name} ({user.email})
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  )}
                </Grid>

                <Grid item xs={12} md={6}>
                  {isViewMode ? (
                    <TextField
                      fullWidth
                      label='Role'
                      value={roles.find(r => r.id === formData.role)?.name || 'Loading...'}
                      disabled
                      helperText={roles.find(r => r.id === formData.role)?.level || ''}
                    />
                  ) : (
                    <FormControl fullWidth required>
                      <InputLabel>Role</InputLabel>
                      <Select
                        value={formData.role}
                        label='Role'
                        onChange={e => handleRoleChange(e.target.value)}
                        disabled={isViewMode} // Allow role changes in edit mode
                      >
                        {roles.map(role => (
                          <MenuItem key={role.id} value={role.id}>
                            {role.name} ({role.level})
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  )}
                </Grid>
              </>
            )}

            {/* Settings */}
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label='Expiration Date'
                type='datetime-local'
                value={formData.expiresAt ? new Date(formData.expiresAt).toISOString().slice(0, 16) : ''}
                onChange={e => handleInputChange('expiresAt', e.target.value ? new Date(e.target.value) : undefined)}
                disabled={isViewMode}
                InputLabelProps={{ shrink: true }}
                helperText='Leave empty for no expiration'
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <Box className='flex gap-4'>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.isActive}
                      onChange={e => handleInputChange('isActive', e.target.checked)}
                      disabled={isViewMode}
                    />
                  }
                  label='Active'
                />
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.isDefault}
                      onChange={e => handleInputChange('isDefault', e.target.checked)}
                      disabled={isViewMode}
                    />
                  }
                  label='Default Role'
                />
              </Box>
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label='Notes'
                value={formData.notes}
                onChange={e => handleInputChange('notes', e.target.value)}
                disabled={isViewMode}
                multiline
                rows={3}
                placeholder='Optional notes about this role assignment'
              />
            </Grid>

            {/* Role Permissions Display */}
            {selectedRole && (
              <Grid item xs={12}>
                <Typography variant='h6' className='mb-4'>
                  Role Permissions: {selectedRole.name}
                </Typography>
                <Box className='mb-4'>
                  <Typography variant='body2' color='text.secondary'>
                    This role includes {selectedRole.permissions.length} permissions:
                  </Typography>
                  <Box className='flex flex-wrap gap-1 mt-2'>
                    {selectedRole.permissions.map(permission => (
                      <Typography key={permission} variant='caption' className='bg-gray-100 px-2 py-1 rounded'>
                        {permission}
                      </Typography>
                    ))}
                  </Box>
                </Box>
              </Grid>
            )}

            {/* All Permissions */}
            <Grid item xs={12} md={6}>
              <Typography variant='h6' className='mb-4'>
                User Permissions
              </Typography>
              <Typography variant='body2' color='text.secondary' className='mb-3'>
                Checked permissions are granted to this user. Role permissions are automatically included.
              </Typography>

              <FormGroup>
                {Object.entries(PERMISSIONS).map(([key, permission]) => {
                  const isRolePermission = selectedRole?.permissions.includes(permission)
                  const isAdditionalPermission = formData.additionalPermissions.includes(permission)
                  const isChecked = isRolePermission || isAdditionalPermission

                  return (
                    <FormControlLabel
                      key={permission}
                      control={
                        <Checkbox
                          checked={isChecked}
                          onChange={() => handlePermissionToggle(permission, 'additional')}
                          disabled={isViewMode || isRolePermission}
                        />
                      }
                      label={
                        <Box>
                          <Typography variant='body2'>
                            {key.replace(/_/g, ' ')}
                            {isRolePermission && (
                              <Typography component='span' variant='caption' color='primary' className='ml-2'>
                                (from role)
                              </Typography>
                            )}
                          </Typography>
                          <Typography variant='caption' color='text.secondary'>
                            {permission}
                          </Typography>
                        </Box>
                      }
                    />
                  )
                })}
              </FormGroup>
            </Grid>

            {/* Denied Permissions */}
            <Grid item xs={12} md={6}>
              <Typography variant='h6' className='mb-4'>
                Denied Permissions
              </Typography>
              <Typography variant='body2' color='text.secondary' className='mb-3'>
                Check permissions to remove from this user, even if granted by their role.
              </Typography>

              <FormGroup>
                {Object.entries(PERMISSIONS).map(([key, permission]) => {
                  const isRolePermission = selectedRole?.permissions.includes(permission)
                  const isDeniedPermission = formData.deniedPermissions.includes(permission)

                  return (
                    <FormControlLabel
                      key={permission}
                      control={
                        <Checkbox
                          checked={isDeniedPermission}
                          onChange={() => handlePermissionToggle(permission, 'denied')}
                          disabled={isViewMode || !isRolePermission}
                        />
                      }
                      label={
                        <Box>
                          <Typography variant='body2'>
                            {key.replace(/_/g, ' ')}
                            {!isRolePermission && (
                              <Typography component='span' variant='caption' color='text.disabled' className='ml-2'>
                                (not in role)
                              </Typography>
                            )}
                          </Typography>
                          <Typography variant='caption' color='text.secondary'>
                            {permission}
                          </Typography>
                        </Box>
                      }
                    />
                  )
                })}
              </FormGroup>
            </Grid>

            {/* Action Buttons */}
            {!isViewMode && (
              <Grid item xs={12}>
                <Box className='flex gap-4'>
                  <Button
                    type='submit'
                    variant='contained'
                    disabled={loading}
                    startIcon={loading ? <CircularProgress size={20} /> : null}
                  >
                    {loading ? 'Saving...' : isEditMode ? 'Update Assignment' : 'Assign Role'}
                  </Button>
                  <Button variant='outlined' onClick={() => router.push('/user-roles?tab=all')}>
                    Cancel
                  </Button>
                </Box>
              </Grid>
            )}

            {isViewMode && (
              <Grid item xs={12}>
                <Box className='flex gap-4'>
                  <Button
                    variant='contained'
                    onClick={() => router.push(`/user-roles?tab=user-role&id=${userRoleId}&mode=edit`)}
                  >
                    Edit Assignment
                  </Button>
                  <Button variant='outlined' onClick={() => router.push('/user-roles?tab=all')}>
                    Back to List
                  </Button>
                </Box>
              </Grid>
            )}
          </Grid>
        </form>
      </CardContent>

      {/* Notification */}
      <Snackbar
        open={notification.show}
        autoHideDuration={6000}
        onClose={() => setNotification({ ...notification, show: false })}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setNotification({ ...notification, show: false })}
          severity={notification.type}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Card>
  )
}

export default UserRoleForm
