'use client'

// React Imports
import { useEffect, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'

// MUI Imports
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import Grid from '@mui/material/Grid'
import TextField from '@mui/material/TextField'
import Button from '@mui/material/Button'
import Typography from '@mui/material/Typography'
import Box from '@mui/material/Box'
import Chip from '@mui/material/Chip'
import Snackbar from '@mui/material/Snackbar'
import Alert from '@mui/material/Alert'
import CircularProgress from '@mui/material/CircularProgress'

// Type Imports
import type { UserRoleWithDetails } from '@/types/user-role'

interface User {
  id: string
  name: string
  email: string
}

const UserRoleView = () => {
  // Hooks
  const searchParams = useSearchParams()
  const router = useRouter()
  const userRoleId = searchParams.get('id')

  // States
  const [userRole, setUserRole] = useState<UserRoleWithDetails | null>(null)
  const [loading, setLoading] = useState(true)
  const [notification, setNotification] = useState<{
    show: boolean
    message: string
    type: 'success' | 'error' | 'warning' | 'info'
  }>({
    show: false,
    message: '',
    type: 'success'
  })

  // Fetch user role data
  useEffect(() => {
    if (userRoleId) {
      fetchUserRole()
    }
  }, [userRoleId])

  const fetchUserRole = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/user-roles/${userRoleId}`)

      if (!response.ok) {
        throw new Error('Failed to fetch user role')
      }

      const data = await response.json()
      const userRoleData = data.data || data
      setUserRole(userRoleData)
    } catch (error) {
      console.error('Error fetching user role:', error)
      setNotification({
        show: true,
        message: 'Failed to fetch user role data',
        type: 'error'
      })
    } finally {
      setLoading(false)
    }
  }

  // Format date
  const formatDate = (date: string | Date | undefined) => {
    if (!date) return 'N/A'
    return new Date(date).toLocaleDateString()
  }

  // Get level color
  const getLevelColor = (level: string) => {
    switch (level) {
      case 'admin':
        return 'error'
      case 'moderator':
        return 'warning'
      case 'user':
        return 'primary'
      case 'viewer':
        return 'secondary'
      default:
        return 'default'
    }
  }

  if (loading) {
    return (
      <Card>
        <CardContent className='flex justify-center items-center py-8'>
          <CircularProgress />
        </CardContent>
      </Card>
    )
  }

  if (!userRole) {
    return (
      <Card>
        <CardContent>
          <Typography color='error'>User role not found</Typography>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader title='User Role Details' subheader='View user role assignment information' />
      <CardContent>
        <Grid container spacing={6}>
          {/* User Information */}
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label='User'
              value={userRole.userDetails?.name || 'Unknown User'}
              disabled
              helperText={userRole.userDetails?.email || ''}
            />
          </Grid>

          {/* Role Information */}
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label='Role'
              value={userRole.roleDetails?.name || 'Unknown Role'}
              disabled
              helperText={userRole.roleDetails?.level || ''}
            />
          </Grid>

          {/* Status and Settings */}
          <Grid item xs={12} md={6}>
            <TextField fullWidth label='Status' value={userRole.isActive ? 'Active' : 'Inactive'} disabled />
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField fullWidth label='Default Role' value={userRole.isDefault ? 'Yes' : 'No'} disabled />
          </Grid>

          {/* Dates */}
          <Grid item xs={12} md={6}>
            <TextField fullWidth label='Assigned Date' value={formatDate(userRole.assignedAt)} disabled />
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label='Expires'
              value={userRole.expiresAt ? formatDate(userRole.expiresAt) : 'Never'}
              disabled
            />
          </Grid>

          {/* Notes */}
          {userRole.notes && (
            <Grid item xs={12}>
              <TextField fullWidth label='Notes' value={userRole.notes} disabled multiline rows={3} />
            </Grid>
          )}

          {/* Role Permissions */}
          {userRole.roleDetails && userRole.roleDetails.permissions && userRole.roleDetails.permissions.length > 0 && (
            <Grid item xs={12}>
              <Typography variant='h6' className='mb-4'>
                Role Permissions
              </Typography>
              <Box className='flex flex-wrap gap-1'>
                {userRole.roleDetails.permissions.map(permission => (
                  <Chip key={permission} label={permission} variant='outlined' size='small' />
                ))}
              </Box>
            </Grid>
          )}

          {/* Additional Permissions */}
          {userRole.additionalPermissions && userRole.additionalPermissions.length > 0 && (
            <Grid item xs={12}>
              <Typography variant='h6' className='mb-4'>
                Additional Permissions
              </Typography>
              <Box className='flex flex-wrap gap-1'>
                {userRole.additionalPermissions.map(permission => (
                  <Chip key={permission} label={permission} color='success' variant='outlined' size='small' />
                ))}
              </Box>
            </Grid>
          )}

          {/* Denied Permissions */}
          {userRole.deniedPermissions && userRole.deniedPermissions.length > 0 && (
            <Grid item xs={12}>
              <Typography variant='h6' className='mb-4'>
                Denied Permissions
              </Typography>
              <Box className='flex flex-wrap gap-1'>
                {userRole.deniedPermissions.map(permission => (
                  <Chip key={permission} label={permission} color='error' variant='outlined' size='small' />
                ))}
              </Box>
            </Grid>
          )}
        </Grid>

        {/* Action Buttons */}
        <div className='flex gap-4 flex-wrap mt-6'>
          <Button
            variant='contained'
            color='primary'
            onClick={() => router.push(`/user-roles?tab=user-role&id=${userRoleId}&mode=edit`)}
          >
            Edit Assignment
          </Button>
          <Button variant='outlined' color='primary' onClick={() => router.push('/user-roles?tab=all')}>
            Back to List
          </Button>
        </div>
      </CardContent>

      {/* Notification */}
      <Snackbar
        open={notification.show}
        autoHideDuration={6000}
        onClose={() => setNotification({ ...notification, show: false })}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setNotification({ ...notification, show: false })}
          severity={notification.type}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Card>
  )
}

export default UserRoleView
