'use client'

// React Imports
import { useSearchParams } from 'next/navigation'

// Component Imports
import ContactForm from './ContactForm'
import ContactView from './ContactView'

const ContactDetails = () => {
  // Hooks
  const searchParams = useSearchParams()
  const mode = searchParams.get('mode')
  const id = searchParams.get('id')
  const isViewMode = mode === 'view'

  // Render the appropriate component based on mode
  return isViewMode ? <ContactView /> : <ContactForm />
}

export default ContactDetails
