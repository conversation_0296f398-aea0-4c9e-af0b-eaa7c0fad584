import type { ChangeEvent } from 'react'

// MUI Imports
import CardContent from '@mui/material/CardContent'
import Button from '@mui/material/Button'
import Typography from '@mui/material/Typography'
import Skeleton from '@mui/material/Skeleton'
import Box from '@mui/material/Box'
import Avatar from '@mui/material/Avatar'
import CircularProgress from '@mui/material/CircularProgress'

// Utils
import { isDefaultAvatar } from '../utils/imageUtils'

interface ProfileImageSectionProps {
  imgSrc: string
  fileInput: string
  fetchLoading: boolean
  fileUploading?: boolean
  onFileInputChange: (file: ChangeEvent) => void
  onFileInputReset: () => void
  onOpenCropper: () => void
}

const ProfileImageSection = ({
  imgSrc,
  fileInput,
  fetchLoading,
  fileUploading = false,
  onFileInputChange,
  onFileInputReset,
  onOpenCropper
}: ProfileImageSectionProps) => {
  const isDefaultImage = isDefaultAvatar(imgSrc)

  return (
    <CardContent className='mbe-5'>
      <div className='flex max-sm:flex-col items-center gap-6'>
        <Box sx={{ position: 'relative' }}>
          {fetchLoading ? (
            <Skeleton variant='circular' width={120} height={120} />
          ) : (
            <Avatar
              src={imgSrc}
              alt='Profile Picture'
              sx={{
                width: 120,
                height: 120,
                border: '4px solid',
                borderColor: 'primary.main',
                boxShadow: 3
              }}
            />
          )}

          {fileUploading && (
            <Box
              sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: 'rgba(0, 0, 0, 0.5)',
                borderRadius: '50%'
              }}
            >
              <CircularProgress size={30} sx={{ color: 'white' }} />
            </Box>
          )}
        </Box>

        <div className='flex flex-grow flex-col gap-4'>
          <div className='flex flex-col sm:flex-row gap-4'>
            <Button
              component='label'
              size='medium'
              variant='contained'
              startIcon={<i className='ri-upload-cloud-2-line' />}
              htmlFor='account-settings-upload-image'
              disabled={fetchLoading || fileUploading}
              sx={{ minWidth: 160 }}
            >
              {fileUploading ? 'Uploading...' : 'Upload Photo'}
              <input
                hidden
                type='file'
                value={fileInput}
                accept='image/png, image/jpeg, image/jpg, image/gif'
                onChange={onFileInputChange}
                id='account-settings-upload-image'
              />
            </Button>

            <Button
              size='medium'
              variant='outlined'
              color='primary'
              startIcon={<i className='ri-crop-line' />}
              onClick={onOpenCropper}
              disabled={fetchLoading || fileUploading || isDefaultImage}
            >
              Crop Image
            </Button>

            {!isDefaultImage && (
              <Button
                size='medium'
                variant='outlined'
                color='error'
                startIcon={<i className='ri-delete-bin-line' />}
                onClick={onFileInputReset}
                disabled={fetchLoading || fileUploading}
              >
                Reset Image
              </Button>
            )}
          </div>

          <Box>
            <Typography variant='body2' color='text.secondary' sx={{ mb: 1 }}>
              Upload Guidelines:
            </Typography>
            <Typography variant='caption' color='text.secondary' component='div'>
              • Supported formats: JPG, PNG, GIF
            </Typography>
            <Typography variant='caption' color='text.secondary' component='div'>
              • Maximum size: 800KB
            </Typography>
            <Typography variant='caption' color='text.secondary' component='div'>
              • Images will be cropped to perfect circle
            </Typography>
            <Typography variant='caption' color='text.secondary' component='div'>
              • Automatically optimized for best quality
            </Typography>
          </Box>
        </div>
      </div>
    </CardContent>
  )
}

export default ProfileImageSection
