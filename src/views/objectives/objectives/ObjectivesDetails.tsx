'use client'

// React Imports
import { useSearchParams } from 'next/navigation'

// Component Imports
import ObjectivesForm from './ObjectivesForm'
import ObjectivesView from './ObjectivesView'

const ObjectivesDetails = () => {
  // Hooks
  const searchParams = useSearchParams()
  const mode = searchParams.get('mode')
  const id = searchParams.get('id')
  const isViewMode = mode === 'view'

  // Render the appropriate component based on mode
  return isViewMode ? <ObjectivesView /> : <ObjectivesForm />
}

export default ObjectivesDetails
