'use client'

// React Imports
import { useSearchParams } from 'next/navigation'

// Component Imports
import CompanyForm from './CompanyForm'
import CompanyView from './CompanyView'

const CompanyDetails = () => {
  // Hooks
  const searchParams = useSearchParams()
  const mode = searchParams.get('mode')
  const id = searchParams.get('id')
  const isViewMode = mode === 'view'

  // Render the appropriate component based on mode
  return isViewMode ? <CompanyView /> : <CompanyForm />
}

export default CompanyDetails
