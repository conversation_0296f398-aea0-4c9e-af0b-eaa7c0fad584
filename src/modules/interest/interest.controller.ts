import {
  Body,
  Controller,
  Delete,
  Get,
  Logger,
  Param,
  Patch,
  Post,
  Query,
  Req,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { Request } from 'express';
import { FileInterceptor } from '@nestjs/platform-express';
import { InterestService } from './interest.service';
import {
  CurrentUser,
  JwtAuthGuard,
  PERMISSIONS,
  PermissionsGuard,
  RequirePermissions,
  RequireRoles,
  ROLE,
} from '@app/common';
import {
  CreateInterestDto,
  GetInterestPaginationDto,
  UpdateInterestDto,
} from './dto';
import { UserEntity } from '@app/repository';

@Controller({
  path: 'interest',
  version: '1',
})
export class InterestController {
  private readonly logger = new Logger(InterestController.name);

  constructor(private readonly interestService: InterestService) {}

  @UseGuards(JwtAuthGuard)
  @Post()
  @RequireRoles(ROLE.ADMIN, ROLE.SUPER_ADMIN, ROLE.MODERATOR, ROLE.USER)
  @UseGuards(PermissionsGuard)
  @RequirePermissions(PERMISSIONS.INTEREST_READ, PERMISSIONS.INTEREST_CREATE)
  @UseInterceptors(FileInterceptor('logo'))
  async create(
    @CurrentUser() user: UserEntity,
    @Body() createInterestDto: CreateInterestDto,
    @UploadedFile() logo?: Express.Multer.File,
  ) {
    return this.interestService.create(user, createInterestDto, logo);
  }

  @UseGuards(JwtAuthGuard)
  @Get()
  @UseGuards(PermissionsGuard)
  @RequirePermissions(PERMISSIONS.INTEREST_READ)
  findAll(
    @CurrentUser() user: UserEntity,
    @Query() paginationDto: GetInterestPaginationDto,
  ) {
    return this.interestService.findAll(user, paginationDto);
  }

  @Get('data')
  async findAllForPortfolio(@Req() request: Request) {
    return this.interestService.findAllByWebsiteUrl(request);
  }

  @UseGuards(JwtAuthGuard)
  @Get(':id')
  @UseGuards(PermissionsGuard)
  @RequirePermissions(PERMISSIONS.INTEREST_READ)
  findOne(@Param('id') id: string) {
    return this.interestService.findOne(id);
  }

  @UseGuards(JwtAuthGuard)
  @Patch(':id')
  @RequireRoles(ROLE.ADMIN, ROLE.SUPER_ADMIN, ROLE.MODERATOR, ROLE.USER)
  @UseGuards(PermissionsGuard)
  @RequirePermissions(PERMISSIONS.INTEREST_READ, PERMISSIONS.INTEREST_UPDATE)
  @UseInterceptors(FileInterceptor('logo'))
  async update(
    @Param('id') id: string,
    @Body() updateInterestDto: UpdateInterestDto,
    @UploadedFile() logo?: Express.Multer.File,
  ) {
    return this.interestService.update(id, updateInterestDto, logo);
  }

  @UseGuards(JwtAuthGuard)
  @RequireRoles(ROLE.ADMIN, ROLE.SUPER_ADMIN, ROLE.MODERATOR, ROLE.USER)
  @UseGuards(PermissionsGuard)
  @RequirePermissions(PERMISSIONS.INTEREST_READ, PERMISSIONS.INTEREST_DELETE)
  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.interestService.remove(id);
  }
}
