import { Injectable, Logger } from '@nestjs/common';
import { IDataServices, UserEntity } from '@app/repository';
import {
  create,
  deleteById,
  getAll,
  getAllForTenant,
  getById,
  updateById,
} from './services';
import {
  CreateSocialMediaDto,
  GetSocialMediaPaginationDto,
  UpdateSocialMediaDto,
} from './dto';
import {
  CloudinaryFolder,
  CloudinaryService,
  uploadFileToCloudinary,
} from '@app/common';
import { ConfigService } from '../config/config.service';

@Injectable()
export class SocialMediaService {
  private readonly logger = new Logger(SocialMediaService.name);
  private cloudinaryAvailable = true;

  constructor(
    private readonly db: IDataServices,
    private readonly cloudinaryService: CloudinaryService,
    private readonly configService: ConfigService,
  ) {}

  async create(
    user: UserEntity,
    data: CreateSocialMediaDto,
    logo?: Express.Multer.File,
  ) {
    let logoUrl: string | undefined;
    if (logo) {
      try {
        const uploadResult = await uploadFileToCloudinary(
          logo.buffer,
          this.cloudinaryService,
          CloudinaryFolder.SOCIAL_MEDIA,
        );
        logoUrl = uploadResult.url;
      } catch (error) {
        this.logger.error('Error uploading logo to Cloudinary:', error);
        // Continue without the logo URL if upload fails
      }
    }

    return create(user, data, this.db, logoUrl);
  }

  findAll(
    user: UserEntity = null,
    paginationDto?: GetSocialMediaPaginationDto,
  ) {
    return getAll(this.db, user, paginationDto);
  }

  findMyOwnSocialMedia(
    user: UserEntity,
    paginationDto?: GetSocialMediaPaginationDto,
  ) {
    // Always return only the current user's social media, regardless of role
    return this.getMyOwnSocialMedia(this.db, user, paginationDto);
  }

  private async getMyOwnSocialMedia(
    db: IDataServices,
    user: UserEntity,
    paginationDto?: GetSocialMediaPaginationDto,
  ) {
    // Check if user exists and has an ID
    if (!user?.id) {
      return paginationDto
        ? {
            data: [],
            total: 0,
            page: paginationDto.page || 1,
            limit: paginationDto.limit || 10,
            totalPages: 0,
          }
        : [];
    }

    // Always filter by current user only, regardless of role
    if (paginationDto && (paginationDto.page || paginationDto.limit)) {
      console.log('Fetching paginated social media for current user:', user.id);

      const paginationResult = await db.socialMedias.findAllWithPagination(
        { user },
        paginationDto.page || 1,
        paginationDto.limit || 10,
      );

      console.log('Paginated result:', paginationResult);

      // Apply presenter to all social media items
      const socialMedias = paginationResult.data.map((socialMedia) => ({
        id: socialMedia.id,
        name: socialMedia.name,
        link: socialMedia.link,
        logo: socialMedia.logo,
        description: socialMedia.description,
        active: socialMedia.active,
        user: socialMedia.user,
        createdAt: socialMedia.createdAt,
        updatedAt: socialMedia.updatedAt,
      }));

      console.log('Social medias after presenter:', socialMedias.length);

      return {
        data: socialMedias,
        total: paginationResult.total,
        page: paginationResult.page,
        limit: paginationResult.limit,
        totalPages: paginationResult.totalPages,
      };
    }

    // Fallback to original non-paginated method
    console.log('Fetching social media for current user:', user.id);
    const data = await db.socialMedias.findAll({ user });

    console.log('Raw data from database:', data.length);

    // Apply presenter and return all social media (including inactive ones for user's own data)
    const result = data.map((socialMedia) => ({
      id: socialMedia.id,
      name: socialMedia.name,
      link: socialMedia.link,
      logo: socialMedia.logo,
      description: socialMedia.description,
      active: socialMedia.active,
      user: socialMedia.user,
      createdAt: socialMedia.createdAt,
      updatedAt: socialMedia.updatedAt,
    }));

    console.log('Final result:', result.length);

    return result;
  }

  findOne(id: string) {
    return getById(id, this.db);
  }

  async update(
    id: string,
    data: UpdateSocialMediaDto,
    logo?: Express.Multer.File,
  ) {
    let logoUrl: string | undefined;

    if (logo && this.cloudinaryAvailable) {
      try {
        const uploadResult = await uploadFileToCloudinary(
          logo.buffer,
          this.cloudinaryService,
          CloudinaryFolder.SOCIAL_MEDIA,
        );
        logoUrl = uploadResult.url;
      } catch (error) {
        this.logger.error(
          'Error uploading logo to Cloudinary for update:',
          error,
        );
        // Continue without the logo URL if upload fails
      }
    } else if (logo && !this.cloudinaryAvailable) {
      this.logger.warn(
        'Cloudinary is not available. Using placeholder logo URL for update.',
      );
      // Provide a placeholder URL when Cloudinary is not available
      logoUrl = '';
    }

    return updateById(id, data, this.db, logoUrl);
  }

  remove(id: string) {
    return deleteById(id, this.db);
  }

  async findAllByWebsiteUrl(request: any) {
    return getAllForTenant(request, this.db, this.configService);
  }
}
