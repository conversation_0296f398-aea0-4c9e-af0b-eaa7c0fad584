import { IDataServices } from '@app/repository';
import { BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import * as argon2 from 'argon2';
import { LoginDto } from '../dto';
import { generateAccessToken } from './generate-token';

export const login = async (
  data: LoginDto,
  db: IDataServices,
  configService: ConfigService,
  jwtService: JwtService,
) => {
  const { email, password } = data;

  const user = await db.users.findByEmail(email);

  if (!user) {
    throw new BadRequestException('User not found');
  }

  const isPassMatched = await argon2.verify(user.password, password);

  if (!isPassMatched) {
    throw new BadRequestException('Invalid credentials');
  }

  const accessToken = generateAccessToken(user, configService, jwtService);

  const userData = {
    user,
    tokens: [accessToken],
  };
  const userAuth = await db.auths.findOne({
    user,
  });
  if (!userAuth) {
    await db.auths.insert(userData);
  } else {
    const userTokensCount = userAuth.tokens.length;

    // TODO : Dynamically set the max tokens count
    if (userTokensCount >= 5) {
      userAuth.tokens.shift();
      userAuth.tokens.push(accessToken);
    } else {
      userAuth.tokens = [...userAuth.tokens, accessToken];
    }
    await db.auths.update(
      {
        user,
      },
      {
        tokens: userAuth.tokens,
      },
    );
  }

  // TODO : Presenter for user registration
  // Exclude password from response
  const { password: _, ...userWithoutPassword } = user;

  return {
    accessToken,
    data: userWithoutPassword,
  };
};
