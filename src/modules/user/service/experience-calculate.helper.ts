export const calculateExperience = (companies) => {
  // 1. Find the earliest start date
  const earliestStartDate = companies.reduce((earliest, company) => {
    if (!earliest || company.startedAt.getTime() < earliest.getTime()) {
      return company.startedAt;
    }
    return earliest;
  }, null);

  // 2. Get the current date
  const currentDate = new Date();

  // 3. Calculate the total experience in milliseconds from the earliest start date to now
  let totalExperienceMs = 0;
  if (earliestStartDate) {
    totalExperienceMs = currentDate.getTime() - earliestStartDate.getTime();
  }

  // 4. Convert milliseconds to years (approximate)
  const millisecondsInYear = 1000 * 60 * 60 * 24 * 365.25; // Account for leap years on average
  const totalExperienceYears = totalExperienceMs / millisecondsInYear;

  // 5. Format the output

  let experienceString = '';
  if (totalExperienceYears > 0) {
    const years = Math.floor(totalExperienceYears);
    const decimalPart = totalExperienceYears - years;

    if (decimalPart > 0.05 && decimalPart < 0.95) {
      // Show decimal if it's significant
      experienceString = `${totalExperienceYears.toFixed(1)} years`;
    } else if (decimalPart >= 0.95) {
      // Round up if very close to next whole year
      experienceString = `${Math.ceil(totalExperienceYears)} years`;
    } else {
      experienceString = `${years} years`;
    }
  } else {
    experienceString = 'Less than a year';
  }

  console.log(experienceString);

  // If you still want the sum of durations for companies with an endedAt date (as in your original code)
  // and then format that:
  const totalDurationOfCompletedJobsMs = companies.reduce((acc, company) => {
    if (company.endedAt) {
      return acc + (company.endedAt.getTime() - company.startedAt.getTime());
    }
    return acc;
  }, 0);

  const totalDurationCompletedYears =
    totalDurationOfCompletedJobsMs / millisecondsInYear;
  let completedJobsString = '';

  if (totalDurationCompletedYears > 0) {
    const years = Math.floor(totalDurationCompletedYears);
    const decimalPart = totalDurationCompletedYears - years;
    if (decimalPart > 0.05 && decimalPart < 0.95) {
      completedJobsString = `${totalDurationCompletedYears.toFixed(1)} years`;
    } else if (decimalPart >= 0.95) {
      completedJobsString = `${Math.ceil(totalDurationCompletedYears)} years`;
    } else {
      completedJobsString = `${years} years`;
    }
  } else {
    completedJobsString = 'Less than a year';
  }
  return {
    totalExperience: experienceString,
    totalDurationOfCompletedJobs: completedJobsString,
  };
};
