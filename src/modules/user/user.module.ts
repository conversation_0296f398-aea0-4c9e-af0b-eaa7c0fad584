import { <PERSON><PERSON><PERSON>, <PERSON>ope } from '@nestjs/common';
import { UserService } from './user.service';
import { UserController } from './user.controller';
import { APP_FILTER, APP_INTERCEPTOR, APP_PIPE } from '@nestjs/core';
import {
  AllExceptionsFilter,
  CloudinaryModule,
  FreezePipe,
  LoggingInterceptor,
} from '@app/common';
import { RepositoryModule } from '@app/repository';
import { JwtService } from '@nestjs/jwt';
import { UserConfigModule } from '../config/config.module';

@Module({
  imports: [RepositoryModule, CloudinaryModule, UserConfigModule],
  controllers: [UserController],
  providers: [
    UserService,
    JwtService,
    {
      provide: APP_INTERCEPTOR,
      scope: Scope.REQUEST,
      useClass: LoggingInterceptor,
    },
    {
      provide: APP_PIPE,
      useClass: FreezePipe,
    },
    {
      provide: APP_FILTER,
      useClass: AllExceptionsFilter,
    },
  ],
})
export class UserModule {}
