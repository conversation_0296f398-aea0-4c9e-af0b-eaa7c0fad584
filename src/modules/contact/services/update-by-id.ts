import { ContactEntity, IDataServices } from '@app/repository';
import { UpdateContactDto } from '../dto';

export const updateById = async (
  id: string,
  data: UpdateContactDto,
  db: IDataServices,
  logoUrl?: string,
) => {
  // Create update data object with all fields from the DTO
  const updateData = {
    ...data,
    // If logoUrl is provided from Cloudinary upload, use it instead of the one in data
    logo: logoUrl || data.logo,
  };

  return await db.contacts.update(
    { id: id },
    updateData as unknown as Partial<ContactEntity>,
  );
};
