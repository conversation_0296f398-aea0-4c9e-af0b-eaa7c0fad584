import { IDataServices, UserEntity } from '@app/repository';
import { CompanyPresenter } from '../presenter';
import { checkAdminRoleLevel } from '@app/common';
import { GetCompanyPaginationDto } from '../dto';

export const getAll = async (
  db: IDataServices,
  user: UserEntity,
  paginationDto?: GetCompanyPaginationDto,
) => {
  // Check if user exists and has an ID
  if (!user?.id) {
    return paginationDto
      ? {
          data: [],
          total: 0,
          page: paginationDto.page || 1,
          limit: paginationDto.limit || 10,
          totalPages: 0,
        }
      : [];
  }

  const hasAdminRole = await checkAdminRoleLevel(db, user.id);

  // Set up pagination parameters
  const page = paginationDto?.page || 1;
  const limit = paginationDto?.limit || 10;

  // Determine filter based on user role
  const filter = hasAdminRole ? {} : { user };

  // Always use pagination when pagination parameters are provided
  if (paginationDto && (paginationDto.page || paginationDto.limit)) {
    console.log(
      'Fetching companies with pagination for user:',
      user.id,
      'hasAdminRole:',
      hasAdminRole,
    );

    const paginationResult = await db.companies.findAllWithPagination(
      filter,
      page,
      limit,
    );

    console.log('Pagination result from database:', {
      total: paginationResult.total,
      page: paginationResult.page,
      totalPages: paginationResult.totalPages,
      dataLength: paginationResult.data.length,
    });

    // Apply presenter to all companies (don't filter by active here since we want to show all in admin)
    const companies = paginationResult.data.map(
      (company) => new CompanyPresenter(company),
    );

    console.log('Companies after presenter:', companies.length);

    return {
      data: companies,
      total: paginationResult.total,
      page: paginationResult.page,
      limit: paginationResult.limit,
      totalPages: paginationResult.totalPages,
    };
  }

  // Fallback to original non-paginated method for backward compatibility
  let data;
  if (hasAdminRole) {
    console.log('Fetching all companies for admin/moderator');
    data = await db.companies.findAll({});
  } else {
    console.log('Fetching companies for user:', user.id);
    data = await db.companies.findAll({ user });
  }

  console.log('Raw data from database:', data.length);

  // Filter active companies and apply presenter
  const result = data
    .filter((company) => company.active)
    .map((company) => new CompanyPresenter(company));

  console.log('Final result after filtering:', result.length);

  return result;
};
