import { Injectable, Logger } from '@nestjs/common';

import { CloudinaryService } from '@app/common/config/cloudinary';
import { uploadFileToCloudinary } from '@app/common/utils';
import { IDataServices, UserEntity } from '@app/repository';
import {
  CreateCompanyDto,
  GetCompanyPaginationDto,
  UpdateCompanyDto,
} from './dto';
import {
  create,
  deleteById,
  getAll,
  getAllForTenant,
  getById,
  updateById,
} from './services';
import { CloudinaryFolder } from '@app/common';
import { ConfigService } from '../config/config.service';

@Injectable()
export class CompanyService {
  private readonly logger = new Logger(CompanyService.name);
  private cloudinaryAvailable = true;

  constructor(
    private readonly db: IDataServices,
    private readonly cloudinaryService: CloudinaryService,
    private readonly configService: ConfigService,
  ) {}

  async create(
    data: CreateCompanyDto,
    user: UserEntity,
    logo?: Express.Multer.File,
  ) {
    let logoUrl: string | undefined;
    if (logo) {
      try {
        const uploadResult = await uploadFileToCloudinary(
          logo.buffer,
          this.cloudinaryService,
          CloudinaryFolder.COMPANIES,
        );
        logoUrl = uploadResult.url;
      } catch (error) {
        this.logger.error('Error uploading logo to Cloudinary:', error);
        // Continue without the logo URL if upload fails
      }
    }

    return create(data, this.db, user, logoUrl);
  }

  findAll(user: UserEntity = null, paginationDto?: GetCompanyPaginationDto) {
    return getAll(this.db, user, paginationDto);
  }

  findOne(id: string) {
    return getById(id, this.db);
  }

  async update(id: string, data: UpdateCompanyDto, logo?: Express.Multer.File) {
    let logoUrl: string | undefined;

    if (logo && this.cloudinaryAvailable) {
      try {
        const uploadResult = await uploadFileToCloudinary(
          logo.buffer,
          this.cloudinaryService,
          CloudinaryFolder.COMPANIES,
        );
        logoUrl = uploadResult.url;
      } catch (error) {
        this.logger.error(
          'Error uploading logo to Cloudinary for update:',
          error,
        );
        // Continue without the logo URL if upload fails
      }
    } else if (logo && !this.cloudinaryAvailable) {
      this.logger.warn(
        'Cloudinary is not available. Using placeholder logo URL for update.',
      );
      // Provide a placeholder URL when Cloudinary is not available
      logoUrl = 'https://via.placeholder.com/150?text=Company+Logo';
    }

    return updateById(id, data, this.db, logoUrl);
  }

  remove(id: string) {
    return deleteById(id, this.db);
  }

  async findAllByWebsiteUrl(request: any) {
    return getAllForTenant(request, this.db, this.configService);
  }
}
