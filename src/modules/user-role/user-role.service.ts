import {
  BadRequestException,
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { CreateUserRoleDto } from './dto/create-user-role.dto';
import { UpdateUserRoleDto } from './dto/update-user-role.dto';
import { AssignPermissionsToUserRoleDto } from './dto/assign-permissions-to-user-role.dto';
import { BulkAssignUserRolesDto } from './dto/bulk-assign-user-roles.dto';
import { IDataServices } from '@app/repository/adapters';
import { UserRoleEntity } from '@app/repository/entities/user-role.entity';
import { PERMISSIONS } from '@app/common/constants/permissions';

@Injectable()
export class UserRoleService {
  constructor(private readonly dataServices: IDataServices) {}

  async create(createUserRoleDto: CreateUserRoleDto): Promise<UserRoleEntity> {
    try {
      // Check if role exists
      const role = await this.dataServices.roles.findById(
        createUserRoleDto.roleId,
      );
      if (!role) {
        throw new NotFoundException(
          `Role with ID '${createUserRoleDto.roleId}' not found`,
        );
      }

      // Check if user already has this role
      const existingUserRole = await this.dataServices.userRoles.findUserRole(
        createUserRoleDto.userId,
        createUserRoleDto.roleId,
      );
      if (existingUserRole) {
        throw new ConflictException('User already has this role assigned');
      }

      // Validate additional permissions
      if (createUserRoleDto.additionalPermissions) {
        this.validatePermissions(createUserRoleDto.additionalPermissions);
      }

      // Validate denied permissions
      if (createUserRoleDto.deniedPermissions) {
        this.validatePermissions(createUserRoleDto.deniedPermissions);
      }

      // If this is set as default, unset other default roles for this user
      if (createUserRoleDto.isDefault) {
        await this.unsetDefaultUserRoles(createUserRoleDto.userId);
      }

      const userRoleData: UserRoleEntity = {
        user: createUserRoleDto.userId,
        role: createUserRoleDto.roleId,
        additionalPermissions: createUserRoleDto.additionalPermissions || [],
        deniedPermissions: createUserRoleDto.deniedPermissions || [],
        isActive:
          createUserRoleDto.isActive !== undefined
            ? createUserRoleDto.isActive
            : true,
        isDefault: createUserRoleDto.isDefault || false,
        expiresAt: createUserRoleDto.expiresAt,
        assignedAt: new Date(),
        assignedBy: createUserRoleDto.assignedBy,
        updatedBy: createUserRoleDto.assignedBy,
        notes: createUserRoleDto.notes,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      return await this.dataServices.userRoles.insert(userRoleData);
    } catch (error) {
      if (
        error instanceof ConflictException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to create user role assignment');
    }
  }

  async findAll(): Promise<UserRoleEntity[]> {
    return await this.dataServices.userRoles.findAll({});
  }

  async findOne(id: string): Promise<UserRoleEntity> {
    const userRole = await this.dataServices.userRoles.findById(id);
    if (!userRole) {
      throw new NotFoundException(
        `User role assignment with ID '${id}' not found`,
      );
    }
    return userRole;
  }

  async findByUser(
    userId: string,
    includeExpired: boolean = false,
  ): Promise<UserRoleEntity[]> {
    return await this.dataServices.userRoles.findByUser(userId, includeExpired);
  }

  async findByRole(
    roleId: string,
    includeExpired: boolean = false,
  ): Promise<UserRoleEntity[]> {
    return await this.dataServices.userRoles.findByRole(roleId, includeExpired);
  }

  async findUserRole(
    userId: string,
    roleId: string,
  ): Promise<UserRoleEntity | null> {
    return await this.dataServices.userRoles.findUserRole(userId, roleId);
  }

  async findDefaultUserRole(userId: string): Promise<UserRoleEntity | null> {
    return await this.dataServices.userRoles.findDefaultUserRole(userId);
  }

  async findExpiredUserRoles(): Promise<UserRoleEntity[]> {
    return await this.dataServices.userRoles.findExpiredUserRoles();
  }

  async update(
    id: string,
    updateUserRoleDto: UpdateUserRoleDto,
  ): Promise<UserRoleEntity> {
    const existingUserRole = await this.dataServices.userRoles.findById(id);
    if (!existingUserRole) {
      throw new NotFoundException(
        `User role assignment with ID '${id}' not found`,
      );
    }

    // If roleId is being changed, validate the new role exists
    if (
      updateUserRoleDto.roleId &&
      updateUserRoleDto.roleId !== existingUserRole.role
    ) {
      const newRole = await this.dataServices.roles.findById(
        updateUserRoleDto.roleId,
      );
      if (!newRole) {
        throw new NotFoundException(
          `Role with ID '${updateUserRoleDto.roleId}' not found`,
        );
      }
    }

    // Validate additional permissions
    if (updateUserRoleDto.additionalPermissions) {
      this.validatePermissions(updateUserRoleDto.additionalPermissions);
    }

    // Validate denied permissions
    if (updateUserRoleDto.deniedPermissions) {
      this.validatePermissions(updateUserRoleDto.deniedPermissions);
    }

    // If this is being set as default, unset other default roles for this user
    if (updateUserRoleDto.isDefault && !existingUserRole.isDefault) {
      await this.unsetDefaultUserRoles(existingUserRole.user, id);
    }

    // Map roleId to role field for database update
    const updateData: Partial<UserRoleEntity> = {
      ...updateUserRoleDto,
      updatedAt: new Date(),
    };
    if (updateUserRoleDto.roleId) {
      console.log(
        `🔍 Mapping roleId ${updateUserRoleDto.roleId} to role field`,
      );
      updateData.role = updateUserRoleDto.roleId;
      delete (updateData as any).roleId; // Remove roleId as it's not a database field
    }

    console.log('🔍 Update data being sent to repository:', updateData);

    const updatedUserRole = await this.dataServices.userRoles.update(
      { id },
      updateData,
    );

    if (!updatedUserRole) {
      throw new NotFoundException(
        `Failed to update user role assignment with ID '${id}'`,
      );
    }

    return updatedUserRole;
  }

  async remove(id: string): Promise<{ message: string }> {
    const userRole = await this.dataServices.userRoles.findById(id);
    if (!userRole) {
      throw new NotFoundException(
        `User role assignment with ID '${id}' not found`,
      );
    }

    const deleted = await this.dataServices.userRoles.deleteOne({ id });
    if (!deleted) {
      throw new BadRequestException(
        `Failed to delete user role assignment with ID '${id}'`,
      );
    }

    return { message: 'User role assignment has been successfully deleted' };
  }

  async assignPermissionsToUserRole(
    assignPermissionsDto: AssignPermissionsToUserRoleDto,
  ): Promise<UserRoleEntity> {
    const { userRoleId, additionalPermissions, deniedPermissions, updatedBy } =
      assignPermissionsDto;

    const userRole = await this.dataServices.userRoles.findById(userRoleId);
    if (!userRole) {
      throw new NotFoundException(
        `User role assignment with ID '${userRoleId}' not found`,
      );
    }

    // Validate permissions
    if (additionalPermissions) {
      this.validatePermissions(additionalPermissions);
    }
    if (deniedPermissions) {
      this.validatePermissions(deniedPermissions);
    }

    const updatedUserRole = await this.dataServices.userRoles.update(
      { id: userRoleId },
      {
        additionalPermissions:
          additionalPermissions || userRole.additionalPermissions,
        deniedPermissions: deniedPermissions || userRole.deniedPermissions,
        updatedBy,
      },
    );

    if (!updatedUserRole) {
      throw new NotFoundException(
        `Failed to update permissions for user role assignment with ID '${userRoleId}'`,
      );
    }

    return updatedUserRole;
  }

  async bulkAssignRoles(
    bulkAssignDto: BulkAssignUserRolesDto,
  ): Promise<UserRoleEntity[]> {
    const { assignments, assignedBy } = bulkAssignDto;
    const results: UserRoleEntity[] = [];

    for (const assignment of assignments) {
      try {
        const createDto: CreateUserRoleDto = {
          userId: assignment.userId,
          roleId: assignment.roleId,
          additionalPermissions: assignment.additionalPermissions,
          deniedPermissions: assignment.deniedPermissions,
          assignedBy,
        };

        const result = await this.create(createDto);
        results.push(result);
      } catch (error) {
        // Continue with other assignments even if one fails
        console.error(
          `Failed to assign role ${assignment.roleId} to user ${assignment.userId}:`,
          error.message,
        );
      }
    }

    return results;
  }

  async getUserEffectivePermissions(userId: string): Promise<string[]> {
    const userRoles =
      await this.dataServices.userRoles.findActiveUserRolesByUser(userId);
    const effectivePermissions = new Set<string>();

    for (const userRole of userRoles) {
      // Get role permissions
      const role = await this.dataServices.roles.findById(userRole.role);
      if (role && role.isActive) {
        // Add role permissions
        role.permissions.forEach((permission) =>
          effectivePermissions.add(permission),
        );

        // Add additional permissions from user role
        userRole.additionalPermissions.forEach((permission) =>
          effectivePermissions.add(permission),
        );

        // Remove denied permissions
        userRole.deniedPermissions.forEach((permission) =>
          effectivePermissions.delete(permission),
        );
      }
    }

    return Array.from(effectivePermissions);
  }

  async checkUserPermission(
    userId: string,
    permission: string,
  ): Promise<boolean> {
    const effectivePermissions = await this.getUserEffectivePermissions(userId);
    return effectivePermissions.includes(permission);
  }

  private validatePermissions(permissions: string[]): void {
    const validPermissions = Object.values(PERMISSIONS);
    const invalidPermissions = permissions.filter(
      (p) => !validPermissions.includes(p as any),
    );

    if (invalidPermissions.length > 0) {
      throw new BadRequestException(
        `Invalid permissions: ${invalidPermissions.join(', ')}`,
      );
    }
  }

  private async unsetDefaultUserRoles(
    userId: string,
    excludeUserRoleId?: string,
  ): Promise<void> {
    const defaultUserRoles = await this.dataServices.userRoles.findAll({
      user: userId,
      isDefault: true,
    });

    for (const userRole of defaultUserRoles) {
      if (!excludeUserRoleId || userRole.id !== excludeUserRoleId) {
        await this.dataServices.userRoles.update(
          { id: userRole.id },
          { isDefault: false },
        );
      }
    }
  }
}
