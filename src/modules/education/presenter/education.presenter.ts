import { EducationEntity, UserEntity } from '@app/repository';

export class EducationPresenter {
  id?: string;
  name: string;
  degree: string;
  logo: string;
  location: string;
  startedAt: Date;
  endedAt: Date;
  active: boolean = true;
  link: string;
  user?: UserEntity;
  createdAt?: Date;
  updatedAt?: Date;
  constructor(type: EducationEntity) {
    this.id = type.id;
    this.name = type.name;
    this.active = type.active;
    this.logo = type.logo;
    this.startedAt = type.startedAt;
    this.endedAt = type.endedAt;
    this.link = type.link;
    this.degree = type.degree;
    this.location = type.location;
    this.user = type.user;
    this.createdAt = type.createdAt;
    this.updatedAt = type.updatedAt;
  }
}
