import { ProjectEntity, SkillEntity, UserEntity } from '@app/repository';

const activeProjectSkills = (data) => data.filter((skill) => skill.active);

export class ProjectPresenter {
  id?: string;
  name: string;
  coverImages?: string[];
  projectDescription?: string;
  startedAt?: Date | null;
  endedAt?: Date | null;
  githubLink?: string;
  projectLiveLink?: string;
  skill: SkillEntity[];
  companyWebsite?: string;
  details?: string;
  active: boolean = true;
  user?: UserEntity;
  createdAt?: Date;
  updatedAt?: Date;

  constructor(type: ProjectEntity) {
    this.id = type.id;
    this.name = type.name;
    this.coverImages = type.coverImages;
    this.projectDescription = type.projectDescription;
    this.startedAt = type.startedAt;
    this.endedAt = type.endedAt;
    this.githubLink = type.githubLink;
    this.projectLiveLink = type.projectLiveLink;
    this.skill = activeProjectSkills(type.skill);
    // this.skill = type.skill;
    this.companyWebsite = type.companyWebsite;
    this.details = type.details;
    this.active = type.active;
    this.user = type.user;
    this.createdAt = type.createdAt;
    this.updatedAt = type.updatedAt;
  }
}
