# PowerShell script to test hot-reloading functionality
# This script helps verify that file changes are being detected by the Docker containers

param(
    [Parameter(Position=0)]
    [ValidateSet("admin-backend", "admin-frontend", "portfolio")]
    [string]$Service = "admin-frontend"
)

function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Success { Write-ColorOutput Green $args }
function Write-Info { Write-ColorOutput Cyan $args }
function Write-Warning { Write-ColorOutput Yellow $args }
function Write-Error { Write-ColorOutput Red $args }

Write-Info "Hot-Reload Test for $Service"
Write-Info "================================"

# Check if containers are running
$containerStatus = docker compose ps --services --filter "status=running"
if ($containerStatus -notcontains $Service) {
    Write-Error "Container '$Service' is not running."
    Write-Info "Start containers first: .\docker-portfolio.ps1 start"
    exit 1
}

Write-Success "✓ Container '$Service' is running"

# Define test file paths based on service
$testFiles = @{
    "admin-backend" = @{
        "path" = "admin-backend/src/app.controller.ts"
        "backup" = "admin-backend/src/app.controller.ts.backup"
        "search" = "Hello World!"
        "replace" = "Hello World! (Hot-Reload Test - $(Get-Date -Format 'HH:mm:ss'))"
    }
    "admin-frontend" = @{
        "path" = "admin-frontend/src/app/page.tsx"
        "backup" = "admin-frontend/src/app/page.tsx.backup"
        "search" = "Admin Dashboard"
        "replace" = "Admin Dashboard (Hot-Reload Test - $(Get-Date -Format 'HH:mm:ss'))"
    }
    "portfolio" = @{
        "path" = "portfolio/src/app/page.tsx"
        "backup" = "portfolio/src/app/page.tsx.backup"
        "search" = "Portfolio"
        "replace" = "Portfolio (Hot-Reload Test - $(Get-Date -Format 'HH:mm:ss'))"
    }
}

$testFile = $testFiles[$Service]

# Check if test file exists
if (-not (Test-Path $testFile.path)) {
    Write-Error "Test file not found: $($testFile.path)"
    Write-Info "This script requires the default project structure."
    exit 1
}

Write-Success "✓ Test file found: $($testFile.path)"

# Create backup
Copy-Item $testFile.path $testFile.backup -Force
Write-Info "✓ Backup created: $($testFile.backup)"

try {
    # Read original content
    $originalContent = Get-Content $testFile.path -Raw
    
    # Check if search string exists
    if ($originalContent -notmatch [regex]::Escape($testFile.search)) {
        Write-Warning "Search string '$($testFile.search)' not found in file."
        Write-Info "The test will add a comment instead."
        $modifiedContent = $originalContent + "`n// Hot-Reload Test - $(Get-Date -Format 'HH:mm:ss')"
    } else {
        # Replace content
        $modifiedContent = $originalContent -replace [regex]::Escape($testFile.search), $testFile.replace
    }
    
    # Write modified content
    Set-Content $testFile.path $modifiedContent -NoNewline
    Write-Success "✓ Test modification applied"
    
    Write-Info ""
    Write-Info "Watching for container rebuild..."
    Write-Info "Check your browser at:"
    
    switch ($Service) {
        "admin-backend" { Write-Info "  http://localhost:8080" }
        "admin-frontend" { Write-Info "  http://localhost:3000" }
        "portfolio" { Write-Info "  http://localhost:5000" }
    }
    
    Write-Info ""
    Write-Info "You should see the changes within 1-3 seconds."
    Write-Info "Press any key to restore the original file..."
    
    # Wait for user input
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    
} finally {
    # Restore original file
    if (Test-Path $testFile.backup) {
        Move-Item $testFile.backup $testFile.path -Force
        Write-Success "✓ Original file restored"
    }
}

Write-Info ""
Write-Info "Hot-reload test completed!"
Write-Info "If changes appeared quickly in your browser, hot-reloading is working correctly."
Write-Info "If not, check the troubleshooting section in DOCKER-WINDOWS-README.md"
