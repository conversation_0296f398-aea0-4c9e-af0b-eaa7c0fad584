/*
 * Filename: /home/<USER>/WorkStation/myportfolio/app/who_am_i/projects/page.jsx
 * Path: /home/<USER>/WorkStation/myportfolio
 * Created Date: Thursday, March 16th 2023, 10:41:37 pm
 * Author: <PERSON><PERSON><PERSON>
 *
 * Copyright (c) 2023 Tanzi<PERSON> Ahmed
 */

"use client";
import React from 'react';
import Thank<PERSON><PERSON> from '@/components/ThanKYou/ThankYou';
import JsonData from '@/components/shared/MockCode/JsonData';

const jsonData = {
  email: "<EMAIL>",
  phone: "+8801822220350",
  linkedIn: "https://www.linkedin.com/in/ahmedtanzim077",
  github: "https://www.github.com/tanzim077",
};

const Page = () => {
  const [isSend, setIsSend] = React.useState(false);

  const handleSend = () => {
    setIsSend(true);
  };
  const handleReSend = () => {
    setIsSend(false);
  };
  return (
    <>
      <div className="flex  full-div">
        {!isSend ? (
          <div className="border-right left-contact-div ">
            <div className="flex flex-col">
              <input type="text" className="input-text" placeholder="Name" />
              <input type="text" className="input-text" placeholder="Email" />
              <input type="text" className="input-text" placeholder="Subject" />
              <textarea
                className="input-textarea"
                placeholder="Type your message here..."
              />
              <button onClick={handleSend} className="btn btn-primary">
                Send
              </button>
            </div>
          </div>
        ) : (
          <ThankYou handleReSend={handleReSend} />
        )}
        <div className="basis-6/12 contact-json-code">
          <JsonData jsonData={jsonData} isContact={true} />
        </div>
      </div>
    </>
  );
};

export default Page;
