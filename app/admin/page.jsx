/*
 * Filename: /home/<USER>/WorkStation/myportfolio/app/hello/page.jsx
 * Path: /home/<USER>/WorkStation/myportfolio
 * Created Date: Wednesday, March 15th 2023, 9:33:17 pm
 * Author: <PERSON><PERSON><PERSON>
 *
 * Copyright (c) 2023 Tanzi<PERSON> Ahmed
 */
'use client';
import React from 'react';
import {router} from 'next/client';

const Page = () => {
    const onSubmit = (e) => {

        e.preventDefault();
        const formData = new FormData(e.target);
        const data = Object.fromEntries(formData.entries());
        router.push('/profile');

    }
    return (
        <div>
            <form onSubmit={onSubmit} className="flex flex-col items-center justify-center h-screen">
                <div className="flex flex-col items-center justify-center h-screen">
                    <h1 className="text-4xl font-bold mb-4">Admin Panel</h1>
                    <input
                        type="text"
                        placeholder="Username"
                        name="username"
                        className="mb-4 p-2 border border-gray-300 rounded"
                    />
                    <input
                        type="password"
                        name="password"
                        placeholder="Password"
                        className="mb-4 p-2 border border-gray-300 rounded"
                    />
                    <button type="submit" className="bg-blue-500 text-white px-4 py-2 rounded">
                        Login
                    </button>
                </div>

            </form>

        </div>
    );
};

export default Page;
