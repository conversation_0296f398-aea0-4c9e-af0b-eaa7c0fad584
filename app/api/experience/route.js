import {PrismaClient} from '@/app/generated/prisma';
import {NextResponse} from 'next/server';

const prisma = new PrismaClient();

export async function GET() {
  try {
    const experiences = await prisma.experience.findMany({
      orderBy: { year: "desc" },
    });
    return NextResponse.json(experiences);
  } catch (error) {
    return NextResponse.json({ error: "Failed to fetch experiences", details: error.message }, { status: 500 });
  }
}
